package dashboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// Service interface defines the dashboard service operations
type Service interface {
	// Main dashboard method
	FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error)

	// IncomeSource operations
	CreateIncomeSource(ctx context.Context, userID string, name string, monthlyAmount monetary.Amount, moneySource financialsheet.MoneySource) (*dashboard.IncomeSource, error)
	FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error)
	UpdateIncomeSource(ctx context.Context, id string, name string, monthlyAmount monetary.Amount) error
	DeleteIncomeSource(ctx context.Context, id string) error

	// EmergencyFund operations
	FindEmergencyFund(ctx context.Context, userID string) (*dashboard.EmergencyFund, error)
	UpdateEmergencyFund(ctx context.Context, userID string, currentValue monetary.Amount) error
	UpdateEmergencyFundGoal(ctx context.Context, userID string, goalValue monetary.Amount) error

	// Investment operations
	CreateInvestment(ctx context.Context, userID string, name string, currentValue monetary.Amount) (*dashboard.Investment, error)
	FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error)
	UpdateInvestment(ctx context.Context, id string, name string, currentValue monetary.Amount) error
	DeleteInvestment(ctx context.Context, id string) error

	// Asset operations
	CreateAsset(ctx context.Context, userID string, description string, value monetary.Amount) (*dashboard.Asset, error)
	FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error)
	UpdateAsset(ctx context.Context, id string, description string, value monetary.Amount) error
	DeleteAsset(ctx context.Context, id string) error

	// Snapshot operations
	CreateMonthlySnapshot(ctx context.Context, userID string) error
	FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error)
}
