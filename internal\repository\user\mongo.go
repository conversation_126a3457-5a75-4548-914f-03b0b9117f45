package user

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/repository"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
	trash      *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.USERS_COLLECTION),
		trash:      db.Collection(repository.USERS_COLLECTION_TRASH),
	}

	// Create indexes
	ctx := context.Background()

	// Email index (unique) for main collection
	_, err := repo.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "email", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("userEmail"),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		db.Client().Disconnect(ctx)
	}

	// Email index for trash collection
	_, err = repo.trash.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "user.email", Value: 1}},
			Options: options.Index().SetName("userEmail"),
		},
	)
	if err != nil {
		db.Client().Disconnect(ctx)
	}

	// ExternalCode index
	_, err = repo.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys: bson.D{{Key: "externalCode", Value: 1}},
		},
	)
	if err != nil {
		db.Client().Disconnect(ctx)
	}

	// ExternalCodeKiwify index
	_, err = repo.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys: bson.D{{Key: "externalCodeKiwify", Value: 1}},
		},
	)
	if err != nil {
		db.Client().Disconnect(ctx)
	}

	// ReferralCode index
	_, err = repo.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys: bson.D{{Key: "referralCode", Value: 1}},
		},
	)
	if err != nil {
		db.Client().Disconnect(ctx)
	}

	return repo
}

func (m mongoDB) Create(ctx context.Context, user *model.User) (string, error) {
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()

	insertedResult, err := m.collection.InsertOne(ctx, user)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, errors.UserConflictExists, errors.Conflict, err)
		}

		return "", errors.New(errors.Repository, errors.UserCreateFailed, errors.Internal, err)
	}

	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (m mongoDB) CreateDelete(ctx context.Context, deletedUser *model.DeletedUser) error {
	deletedUser.DeletedAt = time.Now()

	_, err := m.trash.InsertOne(ctx, deletedUser)
	if err != nil {
		// No need to check duplicate Key since the restore function is not implemented yet.
		// if mongo.IsDuplicateKeyError(err) {
		// 	return errors.New(errors.Repository, errors.DeletedUserConflictExists, errors.Conflict, err)
		// }

		return errors.New(errors.Repository, errors.DeletedUserCreateFailed, errors.Internal, err)
	}

	return nil
}

func (m mongoDB) Find(ctx context.Context, id primitive.ObjectID) (*model.User, error) {
	var user model.User
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: id}}).Decode(&user); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.UserByIDNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.UserFindByIDFailed, errors.Internal, err)
	}

	user.ID = user.ObjectID.Hex()
	return &user, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*model.User, error) {
	cursor, err := m.collection.Find(ctx, bson.D{})
	if err != nil {
		return nil, errors.New(errors.Repository, errors.UserNotFound, errors.Internal, err)
	}

	var users []*model.User
	for cursor.Next(ctx) {
		var user model.User
		if err = cursor.Decode(&user); err != nil {
			return nil, errors.New(errors.Repository, "decode user failed", errors.Internal, err)
		}
		user.ID = user.ObjectID.Hex()
		users = append(users, &user)
	}

	return users, nil
}

func (m mongoDB) FindAdmins(ctx context.Context) ([]*model.User, error) {
	cursor, err := m.collection.Find(ctx, bson.D{primitive.E{Key: "groups.identifier", Value: "admin"}})
	if err != nil {
		return nil, errors.New(errors.Repository, errors.AdminUsersNotFound, errors.Internal, err)
	}

	var users []*model.User
	for cursor.Next(ctx) {
		var user model.User
		if err = cursor.Decode(&user); err != nil {
			return nil, errors.New(errors.Repository, "decode admin user failed", errors.Internal, err)
		}
		user.ID = user.ObjectID.Hex()
		users = append(users, &user)
	}

	return users, nil
}

func (m mongoDB) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "email", Value: email}}).Decode(&user); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.UserByEmailNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.UserFindByEmailFailed, errors.Internal, err)
	}

	user.ID = user.ObjectID.Hex()
	return &user, nil
}

func (m mongoDB) FindByExternalCode(ctx context.Context, externalCode string) (*model.User, error) {
	var user model.User
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "externalCode", Value: externalCode}}).Decode(&user); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.UserByExternalCodeNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.UserFindByExternalFailed, errors.Internal, err)
	}

	user.ID = user.ObjectID.Hex()
	return &user, nil
}

func (m mongoDB) FindByExternalCodeKiwify(ctx context.Context, externalCodeKiwify string) (*model.User, error) {
	var user model.User
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "externalCodeKiwify", Value: externalCodeKiwify}}).Decode(&user); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.UserKiwifyExternalCodeNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.UserFindByExternalCodeFailed, errors.Internal, err)
	}

	user.ID = user.ObjectID.Hex()
	return &user, nil
}

func (m mongoDB) FindByReferral(ctx context.Context, referralCode string) (*model.User, error) {
	var user model.User
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "referralCode", Value: referralCode}}).Decode(&user); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.UserByReferralCodeNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.UserFindByReferralCodeFailed, errors.Internal, err)
	}

	user.ID = user.ObjectID.Hex()
	return &user, nil
}

func (m mongoDB) FindDeletedByEmail(ctx context.Context, email string) (*model.User, error) {
	var deletedUser model.DeletedUser

	if err := m.trash.FindOne(ctx,
		bson.D{primitive.E{Key: "user.email", Value: email}}).Decode(&deletedUser); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.DeletedUserByEmailNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.DeletedUserFindByEmailFailed, errors.Internal, err)
	}

	deletedUser.User.ID = deletedUser.User.ObjectID.Hex()
	return deletedUser.User, nil
}

func (m mongoDB) FindDeletedByExternalCode(ctx context.Context, externalCode string) (*model.User, error) {
	var deletedUser model.DeletedUser

	if err := m.trash.FindOne(ctx,
		bson.D{primitive.E{Key: "user.externalCode", Value: externalCode}}).Decode(&deletedUser); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.DeletedUserByExternalNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.DeletedUserFindByExternalCodeFailed, errors.Internal, err)
	}

	deletedUser.User.ID = deletedUser.User.ObjectID.Hex()
	return deletedUser.User, nil
}

func (m mongoDB) FindDeletedByExternalCodeKiwify(ctx context.Context, externalCodeKiwify string) (*model.User, error) {
	var deletedUser model.DeletedUser

	if err := m.trash.FindOne(ctx,
		bson.D{primitive.E{Key: "user.externalCodeKiwify", Value: externalCodeKiwify}}).Decode(&deletedUser); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.DeletedUserKiwifyExternalCodeNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.DeletedUserFindByKiwifyExternalCodeFailed, errors.Internal, err)
	}

	deletedUser.User.ID = deletedUser.User.ObjectID.Hex()
	return deletedUser.User, nil
}

func (m mongoDB) Update(ctx context.Context, user *model.User) error {
	if user.ObjectID.IsZero() {
		return errors.New(errors.Repository, errors.DreamboardInvalidID, errors.BadRequest, nil)
	}

	user.UpdatedAt = time.Now()

	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: user.ObjectID}},
		primitive.M{"$set": user})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, errors.UserConflictUpdate, errors.Conflict, err)
		}

		return errors.New(errors.Repository, errors.UserUpdateFailed, errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, errors.UserForUpdateNotFound, errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: id}})
	if err != nil {
		return errors.New(errors.Repository, errors.UserDeleteFailed, errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, errors.UserForDeletionNotFound, errors.NotFound, nil)
	}

	return nil
}
