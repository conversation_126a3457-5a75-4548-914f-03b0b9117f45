package dashboard

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

// Mock service
type MockDashboardService struct {
	mock.Mock
}

func (m *MockDashboardService) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.FinancialMap), args.Error(1)
}

func (m *MockDashboardService) CreateIncomeSource(ctx context.Context, userID string, name string, monthlyAmount monetary.Amount, moneySource financialsheet.MoneySource) (*dashboard.IncomeSource, error) {
	args := m.Called(ctx, userID, name, monthlyAmount, moneySource)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.IncomeSource), args.Error(1)
}

func (m *MockDashboardService) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.IncomeSource), args.Error(1)
}

func (m *MockDashboardService) UpdateIncomeSource(ctx context.Context, id string, name string, monthlyAmount monetary.Amount) error {
	args := m.Called(ctx, id, name, monthlyAmount)
	return args.Error(0)
}

func (m *MockDashboardService) DeleteIncomeSource(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardService) FindEmergencyFund(ctx context.Context, userID string) (*dashboard.EmergencyFund, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.EmergencyFund), args.Error(1)
}

func (m *MockDashboardService) UpdateEmergencyFund(ctx context.Context, userID string, currentValue monetary.Amount) error {
	args := m.Called(ctx, userID, currentValue)
	return args.Error(0)
}

func (m *MockDashboardService) UpdateEmergencyFundGoal(ctx context.Context, userID string, goalValue monetary.Amount) error {
	args := m.Called(ctx, userID, goalValue)
	return args.Error(0)
}

func (m *MockDashboardService) CreateInvestment(ctx context.Context, userID string, name string, currentValue monetary.Amount) (*dashboard.Investment, error) {
	args := m.Called(ctx, userID, name, currentValue)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.Investment), args.Error(1)
}

func (m *MockDashboardService) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.Investment), args.Error(1)
}

func (m *MockDashboardService) UpdateInvestment(ctx context.Context, id string, name string, currentValue monetary.Amount) error {
	args := m.Called(ctx, id, name, currentValue)
	return args.Error(0)
}

func (m *MockDashboardService) DeleteInvestment(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardService) CreateAsset(ctx context.Context, userID string, description string, value monetary.Amount) (*dashboard.Asset, error) {
	args := m.Called(ctx, userID, description, value)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.Asset), args.Error(1)
}

func (m *MockDashboardService) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.Asset), args.Error(1)
}

func (m *MockDashboardService) UpdateAsset(ctx context.Context, id string, description string, value monetary.Amount) error {
	args := m.Called(ctx, id, description, value)
	return args.Error(0)
}

func (m *MockDashboardService) DeleteAsset(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardService) CreateMonthlySnapshot(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockDashboardService) FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error) {
	args := m.Called(ctx, userID, limit)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.NetWorthSnapshot), args.Error(1)
}

// Test suite
type DashboardControllerTestSuite struct {
	suite.Suite
	controller  Controller
	mockService *MockDashboardService
	echo        *echo.Echo
	testUserID  string
}

func (suite *DashboardControllerTestSuite) SetupTest() {
	suite.mockService = new(MockDashboardService)
	suite.controller = New(suite.mockService)
	suite.echo = echo.New()
	suite.testUserID = "test-user-123"
}

func TestDashboardControllerTestSuite(t *testing.T) {
	suite.Run(t, new(DashboardControllerTestSuite))
}

func (suite *DashboardControllerTestSuite) createRequestWithAuth(method, url string, body interface{}) (*http.Request, *httptest.ResponseRecorder) {
	var reqBody []byte
	if body != nil {
		reqBody, _ = json.Marshal(body)
	}

	req := httptest.NewRequest(method, url, bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

	// Add mock JWT token
	mockToken := &token.Details{
		Uid: suite.testUserID,
	}
	req = req.WithContext(context.WithValue(req.Context(), "user", mockToken))

	rec := httptest.NewRecorder()
	return req, rec
}

func (suite *DashboardControllerTestSuite) TestFindFinancialMap() {
	// Mock data
	financialMap := &dashboard.FinancialMap{
		UserID:           suite.testUserID,
		MonthlyIncome:    5000,
		TotalInvestments: 25000,
		TotalAssets:      80000,
	}

	suite.mockService.On("FindFinancialMap", mock.Anything, suite.testUserID).Return(financialMap, nil)

	req, rec := suite.createRequestWithAuth("GET", "/dashboard/financialmaps/me", nil)
	c := suite.echo.NewContext(req, rec)

	// Mock token extraction
	c.Set("user", &token.Details{Uid: suite.testUserID})

	handler := suite.controller.FindFinancialMap()
	err := handler(c)

	suite.Require().NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response dashboard.FinancialMap
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.Require().NoError(err)
	suite.Equal(financialMap.UserID, response.UserID)
	suite.Equal(financialMap.MonthlyIncome, response.MonthlyIncome)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *DashboardControllerTestSuite) TestCreateIncomeSource() {
	request := CreateIncomeSourceRequest{
		Name:          "Test Salary",
		MonthlyAmount: 5000,
		MoneySource:   financialsheet.MoneySourceOpt1,
	}

	expectedIncomeSource := &dashboard.IncomeSource{
		UserID:        suite.testUserID,
		Name:          request.Name,
		MonthlyAmount: request.MonthlyAmount,
	}

	suite.mockService.On("CreateIncomeSource", mock.Anything, suite.testUserID, request.Name, request.MonthlyAmount, request.MoneySource).Return(expectedIncomeSource, nil)

	req, rec := suite.createRequestWithAuth("POST", "/dashboard/financialmaps/me/incomes", request)
	c := suite.echo.NewContext(req, rec)
	c.Set("user", &token.Details{Uid: suite.testUserID})

	handler := suite.controller.CreateIncomeSource()
	err := handler(c)

	suite.Require().NoError(err)
	suite.Equal(http.StatusCreated, rec.Code)

	var response dashboard.IncomeSource
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.Require().NoError(err)
	suite.Equal(expectedIncomeSource.Name, response.Name)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *DashboardControllerTestSuite) TestUpdateEmergencyFund() {
	request := UpdateEmergencyFundRequest{
		CurrentValue: 15000,
	}

	suite.mockService.On("UpdateEmergencyFund", mock.Anything, suite.testUserID, request.CurrentValue).Return(nil)

	req, rec := suite.createRequestWithAuth("PUT", "/dashboard/financialmaps/me/emergencyfunds", request)
	c := suite.echo.NewContext(req, rec)
	c.Set("user", &token.Details{Uid: suite.testUserID})

	handler := suite.controller.UpdateEmergencyFund()
	err := handler(c)

	suite.Require().NoError(err)
	suite.Equal(http.StatusNoContent, rec.Code)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *DashboardControllerTestSuite) TestCreateInvestment() {
	request := CreateInvestmentRequest{
		Name:         "Tesouro Selic",
		CurrentValue: 25000,
	}

	expectedInvestment := &dashboard.Investment{
		UserID:       suite.testUserID,
		Name:         request.Name,
		CurrentValue: request.CurrentValue,
	}

	suite.mockService.On("CreateInvestment", mock.Anything, suite.testUserID, request.Name, request.CurrentValue).Return(expectedInvestment, nil)

	req, rec := suite.createRequestWithAuth("POST", "/dashboard/financialmaps/me/investments", request)
	c := suite.echo.NewContext(req, rec)
	c.Set("user", &token.Details{Uid: suite.testUserID})

	handler := suite.controller.CreateInvestment()
	err := handler(c)

	suite.Require().NoError(err)
	suite.Equal(http.StatusCreated, rec.Code)

	var response dashboard.Investment
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.Require().NoError(err)
	suite.Equal(expectedInvestment.Name, response.Name)

	suite.mockService.AssertExpectations(suite.T())
}

func TestTokenExtraction(t *testing.T) {
	// This is a simple test to verify token extraction logic
	// In a real scenario, you would test the actual middleware
	mockToken := &token.Token{
		Uid: "test-user-123",
	}

	assert.Equal(t, "test-user-123", mockToken.Uid)
}
