package scheduler

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/service/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
)

// Scheduler interface defines the scheduler operations
type Scheduler interface {
	Start(ctx context.Context) error
	Stop() error
}

type scheduler struct {
	DashboardService dashboard.Service
	UserService      user.Service
	ticker           *time.Ticker
	stopChan         chan bool
	running          bool
}

// New creates a new scheduler instance
func New(dashboardService dashboard.Service, userService user.Service) Scheduler {
	return &scheduler{
		DashboardService: dashboardService,
		UserService:      userService,
		stopChan:         make(chan bool),
		running:          false,
	}
}

// Start begins the scheduler with monthly snapshot creation
func (s *scheduler) Start(ctx context.Context) error {
	if s.running {
		return nil // Already running
	}

	// Calculate time until next month's first day at midnight
	now := time.Now()
	nextMonth := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location())
	timeUntilNextMonth := nextMonth.Sub(now)

	log.Printf("Scheduler starting. Next monthly snapshot will run in %v at %v", timeUntilNextMonth, nextMonth)

	// Start a goroutine to handle the initial delay and then monthly ticks
	go func() {
		// Wait until the first day of next month
		select {
		case <-time.After(timeUntilNextMonth):
			// Run the first snapshot
			s.createMonthlySnapshots(ctx)
			
			// Now set up monthly ticker (30 days * 24 hours * 60 minutes * 60 seconds)
			s.ticker = time.NewTicker(30 * 24 * time.Hour)
			s.running = true
			
			// Continue with monthly ticks
			for {
				select {
				case <-s.ticker.C:
					s.createMonthlySnapshots(ctx)
				case <-s.stopChan:
					s.ticker.Stop()
					s.running = false
					log.Println("Scheduler stopped")
					return
				}
			}
		case <-s.stopChan:
			s.running = false
			log.Println("Scheduler stopped before first run")
			return
		}
	}()

	return nil
}

// Stop stops the scheduler
func (s *scheduler) Stop() error {
	if !s.running {
		return nil // Already stopped
	}

	s.stopChan <- true
	return nil
}

// createMonthlySnapshots creates snapshots for all users
func (s *scheduler) createMonthlySnapshots(ctx context.Context) {
	log.Println("Starting monthly snapshot creation for all users")
	
	// Get all users
	users, err := s.UserService.FindAll(ctx)
	if err != nil {
		log.Printf("Error fetching users for monthly snapshots: %v", err)
		return
	}

	successCount := 0
	errorCount := 0

	// Create snapshots for each user
	for _, user := range users {
		err := s.DashboardService.CreateMonthlySnapshot(ctx, user.ID)
		if err != nil {
			log.Printf("Error creating monthly snapshot for user %s: %v", user.ID, err)
			errorCount++
		} else {
			successCount++
		}
	}

	log.Printf("Monthly snapshot creation completed. Success: %d, Errors: %d, Total: %d", 
		successCount, errorCount, len(users))
}
