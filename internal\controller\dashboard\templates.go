package dashboard

import (
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// IncomeSource request templates
type CreateIncomeSourceRequest struct {
	Name          string                     `json:"name" validate:"required"`
	MonthlyAmount monetary.Amount            `json:"monthlyAmount" validate:"required,min=1"`
	MoneySource   financialsheet.MoneySource `json:"moneySource" validate:"required"`
}

type UpdateIncomeSourceRequest struct {
	Name          string          `json:"name" validate:"required"`
	MonthlyAmount monetary.Amount `json:"monthlyAmount" validate:"required,min=1"`
}

// EmergencyFund request templates
type UpdateEmergencyFundRequest struct {
	CurrentValue monetary.Amount `json:"currentValue" validate:"required,min=0"`
}

type UpdateEmergencyFundGoalRequest struct {
	GoalValue monetary.Amount `json:"goalValue" validate:"required,min=1"`
}

// Investment request templates
type CreateInvestmentRequest struct {
	Name         string          `json:"name" validate:"required"`
	CurrentValue monetary.Amount `json:"currentValue" validate:"required,min=0"`
}

type UpdateInvestmentRequest struct {
	Name         string          `json:"name" validate:"required"`
	CurrentValue monetary.Amount `json:"currentValue" validate:"required,min=0"`
}

// Asset request templates
type CreateAssetRequest struct {
	Description string          `json:"description" validate:"required"`
	Value       monetary.Amount `json:"value" validate:"required,min=0"`
}

type UpdateAssetRequest struct {
	Description string          `json:"description" validate:"required"`
	Value       monetary.Amount `json:"value" validate:"required,min=0"`
}
