package user

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	// Read operations
	Find(ctx context.Context, id primitive.ObjectID) (*model.User, error)
	FindAll(ctx context.Context) ([]*model.User, error)
	FindAdmins(ctx context.Context) ([]*model.User, error)
	FindByEmail(ctx context.Context, email string) (*model.User, error)
	FindByReferral(ctx context.Context, referralCode string) (*model.User, error)

	// External service integrations - Stripe
	FindByExternalCode(ctx context.Context, externalCode string) (*model.User, error)
	FindDeletedByExternalCode(ctx context.Context, externalCode string) (*model.User, error)

	// External service integrations - Kiwify
	FindByExternalCodeKiwify(ctx context.Context, externalCodeKiwify string) (*model.User, error)
	FindDeletedByExternalCodeKiwify(ctx context.Context, externalCodeKiwify string) (*model.User, error)

	// Deleted user operations
	FindDeletedByEmail(ctx context.Context, email string) (*model.User, error)
}

type Writer interface {
	// Create operations
	Create(ctx context.Context, user *model.User) (string, error)
	CreateDelete(ctx context.Context, deletedUser *model.DeletedUser) error

	// Update operations
	Update(ctx context.Context, user *model.User) error

	// Delete operations
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type Repository interface {
	Reader
	Writer
}
