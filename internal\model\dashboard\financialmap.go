package dashboard

import (
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// FinancialMap is the main DTO that aggregates all dashboard information
type FinancialMap struct {
	ObjectID         primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID               string             `json:"id,omitempty" bson:"-"`
	UserID           string             `json:"userID" bson:"userID"`
	MonthlyIncome    monetary.Amount    `json:"monthlyIncome" bson:"monthlyIncome"`
	EmergencyFund    *EmergencyFund     `json:"emergencyFund" bson:"emergencyFund"`
	TotalInvestments monetary.Amount    `json:"totalInvestments" bson:"totalInvestments"`
	TotalAssets      monetary.Amount    `json:"totalAssets" bson:"totalAssets"`
	NetWorthHistory  []NetWorthSnapshot `json:"netWorthHistory" bson:"netWorthHistory"`
	IncomeSources    []IncomeSource     `json:"incomeSources" bson:"incomeSources"`
	Investments      []Investment       `json:"investments" bson:"investments"`
	Assets           []Asset            `json:"assets" bson:"assets"`
	CreatedAt        time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt        time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// IncomeSource represents a single source of income
type IncomeSource struct {
	ObjectID      primitive.ObjectID          `json:"-" bson:"_id,omitempty"`
	ID            string                      `json:"id,omitempty" bson:"-"`
	UserID        string                      `json:"userID" bson:"userID"`
	Name          string                      `json:"name" bson:"name"`
	MonthlyAmount monetary.Amount             `json:"monthlyAmount" bson:"monthlyAmount"`
	MoneySource   financialsheet.MoneySource  `json:"moneySource" bson:"moneySource"`
	Icon          financialsheet.CategoryIcon `json:"icon" bson:"icon"`
	Blocked       bool                        `json:"blocked" bson:"blocked"`
	LastUpdated   time.Time                   `json:"lastUpdated" bson:"lastUpdated"`
	CreatedAt     time.Time                   `json:"createdAt" bson:"createdAt"`
	UpdatedAt     time.Time                   `json:"updatedAt" bson:"updatedAt"`
}

// EmergencyFund represents the emergency fund card
type EmergencyFund struct {
	ObjectID     primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID           string             `json:"id,omitempty" bson:"-"`
	UserID       string             `json:"userID" bson:"userID"`
	CurrentValue monetary.Amount    `json:"currentValue" bson:"currentValue"`
	GoalValue    monetary.Amount    `json:"goalValue" bson:"goalValue"`
	CreatedAt    time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt    time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// Investment represents a single investment asset
type Investment struct {
	ObjectID     primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID           string             `json:"id,omitempty" bson:"-"`
	UserID       string             `json:"userID" bson:"userID"`
	Name         string             `json:"name" bson:"name"`
	CurrentValue monetary.Amount    `json:"currentValue" bson:"currentValue"`
	CreatedAt    time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt    time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// Asset represents a single physical asset
type Asset struct {
	ObjectID    primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID          string             `json:"id,omitempty" bson:"-"`
	UserID      string             `json:"userID" bson:"userID"`
	Description string             `json:"description" bson:"description"`
	Value       monetary.Amount    `json:"value" bson:"value"`
	CreatedAt   time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt   time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// NetWorthSnapshot represents an immutable historical record for the chart
type NetWorthSnapshot struct {
	ObjectID           primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID                 string             `json:"id,omitempty" bson:"-"`
	UserID             string             `json:"userID" bson:"userID"`
	Date               time.Time          `json:"date" bson:"date"`
	EmergencyFundValue monetary.Amount    `json:"emergencyFundValue" bson:"emergencyFundValue"`
	InvestmentsValue   monetary.Amount    `json:"investmentsValue" bson:"investmentsValue"`
	AssetsValue        monetary.Amount    `json:"assetsValue" bson:"assetsValue"`
	TotalValue         monetary.Amount    `json:"totalValue" bson:"totalValue"`
	CreatedAt          time.Time          `json:"createdAt" bson:"createdAt"`
}

// PrepareCreate prepares the IncomeSource for creation
func (is *IncomeSource) PrepareCreate() error {
	is.Name = strings.TrimSpace(is.Name)
	if is.Name == "" {
		return errors.New(errors.Model, "income source name is required", errors.Validation, nil)
	}

	if is.MonthlyAmount <= 0 {
		return errors.New(errors.Model, "monthly amount must be positive", errors.Validation, nil)
	}

	if !is.MoneySource.IsValid() {
		return errors.New(errors.Model, "invalid money source", errors.Validation, nil)
	}

	now := time.Now()
	is.CreatedAt = now
	is.UpdatedAt = now
	is.LastUpdated = now

	return nil
}

// PrepareUpdate prepares the IncomeSource for update
func (is *IncomeSource) PrepareUpdate() error {
	is.Name = strings.TrimSpace(is.Name)
	if is.Name == "" {
		return errors.New(errors.Model, "income source name is required", errors.Validation, nil)
	}

	if is.MonthlyAmount <= 0 {
		return errors.New(errors.Model, "monthly amount must be positive", errors.Validation, nil)
	}

	if !is.MoneySource.IsValid() {
		return errors.New(errors.Model, "invalid money source", errors.Validation, nil)
	}

	is.UpdatedAt = time.Now()
	is.LastUpdated = time.Now()

	return nil
}

// PrepareCreate prepares the EmergencyFund for creation
func (ef *EmergencyFund) PrepareCreate() error {
	if ef.CurrentValue < 0 {
		return errors.New(errors.Model, "current value cannot be negative", errors.Validation, nil)
	}

	if ef.GoalValue <= 0 {
		return errors.New(errors.Model, "goal value must be positive", errors.Validation, nil)
	}

	now := time.Now()
	ef.CreatedAt = now
	ef.UpdatedAt = now

	return nil
}

// PrepareUpdate prepares the EmergencyFund for update
func (ef *EmergencyFund) PrepareUpdate() error {
	if ef.CurrentValue < 0 {
		return errors.New(errors.Model, "current value cannot be negative", errors.Validation, nil)
	}

	if ef.GoalValue <= 0 {
		return errors.New(errors.Model, "goal value must be positive", errors.Validation, nil)
	}

	ef.UpdatedAt = time.Now()

	return nil
}

// PrepareCreate prepares the Investment for creation
func (i *Investment) PrepareCreate() error {
	i.Name = strings.TrimSpace(i.Name)
	if i.Name == "" {
		return errors.New(errors.Model, "investment name is required", errors.Validation, nil)
	}

	if i.CurrentValue < 0 {
		return errors.New(errors.Model, "current value cannot be negative", errors.Validation, nil)
	}

	now := time.Now()
	i.CreatedAt = now
	i.UpdatedAt = now

	return nil
}

// PrepareUpdate prepares the Investment for update
func (i *Investment) PrepareUpdate() error {
	i.Name = strings.TrimSpace(i.Name)
	if i.Name == "" {
		return errors.New(errors.Model, "investment name is required", errors.Validation, nil)
	}

	if i.CurrentValue < 0 {
		return errors.New(errors.Model, "current value cannot be negative", errors.Validation, nil)
	}

	i.UpdatedAt = time.Now()

	return nil
}

// PrepareCreate prepares the Asset for creation
func (a *Asset) PrepareCreate() error {
	a.Description = strings.TrimSpace(a.Description)
	if a.Description == "" {
		return errors.New(errors.Model, "asset description is required", errors.Validation, nil)
	}

	if a.Value < 0 {
		return errors.New(errors.Model, "asset value cannot be negative", errors.Validation, nil)
	}

	now := time.Now()
	a.CreatedAt = now
	a.UpdatedAt = now

	return nil
}

// PrepareUpdate prepares the Asset for update
func (a *Asset) PrepareUpdate() error {
	a.Description = strings.TrimSpace(a.Description)
	if a.Description == "" {
		return errors.New(errors.Model, "asset description is required", errors.Validation, nil)
	}

	if a.Value < 0 {
		return errors.New(errors.Model, "asset value cannot be negative", errors.Validation, nil)
	}

	a.UpdatedAt = time.Now()

	return nil
}

// PrepareCreate prepares the NetWorthSnapshot for creation
func (nws *NetWorthSnapshot) PrepareCreate() error {
	if nws.Date.IsZero() {
		nws.Date = time.Now()
	}

	// Calculate total value
	nws.TotalValue = nws.EmergencyFundValue + nws.InvestmentsValue + nws.AssetsValue

	nws.CreatedAt = time.Now()

	return nil
}

// PrepareCreate prepares the FinancialMap for creation
func (fm *FinancialMap) PrepareCreate() error {
	if fm.UserID == "" {
		return errors.New(errors.Model, "userID is required", errors.Validation, nil)
	}

	fm.CreatedAt = time.Now()
	fm.UpdatedAt = time.Now()

	return nil
}

// PrepareUpdate prepares the FinancialMap for update
func (fm *FinancialMap) PrepareUpdate() error {
	if fm.UserID == "" {
		return errors.New(errors.Model, "userID is required", errors.Validation, nil)
	}

	fm.UpdatedAt = time.Now()

	return nil
}
