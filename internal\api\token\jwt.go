package token

import (
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"

	"github.com/golang-jwt/jwt"
)

type Token struct {
	Access  string
	Refresh string
}

type Details struct {
	Token *jwt.Token
	Uid   string
	Role  string
}

// Create a new token data
func Create(user *model.User) (*Token, error) {
	var token Token
	var err error

	var expirationMinutes int64 = 15
	if os.Getenv("TOKEN_EXPIRATION_MINUTES") != "" {
		expirationMinutes, _ = strconv.ParseInt(os.Getenv("TOKEN_EXPIRATION_MINUTES"), 10, 64)
	}
	accessTokenClaims := jwt.MapClaims{
		"authorized": true,
		"uid":        user.ID,
		"role":       "user",
		"exp":        time.Now().Add(time.Minute * time.Duration(expirationMinutes)).Unix(),
	}
	if user.IsAdmin() {
		accessTokenClaims["role"] = "admin"
	}
	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessTokenClaims)
	token.Access, err = accessToken.SignedString([]byte(os.Getenv("API_ACCESS_JWT_KEY")))
	if err != nil {
		return nil, errors.New(errors.Token, errors.TokenCannotCreateAccess, errors.Unauthorized, err)
	}

	refreshTokenClaims := jwt.MapClaims{
		"authorized": true,
		"uid":        user.ID,
		"role":       "user",
		"exp":        time.Now().Add(time.Hour * 24 * 7).Unix(),
	}

	if user.IsAdmin() {
		refreshTokenClaims["role"] = "admin"
	}
	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshTokenClaims)
	token.Refresh, err = refreshToken.SignedString([]byte(os.Getenv("API_REFRESH_JWT_KEY")))
	if err != nil {
		return nil, errors.New(errors.Token, errors.TokenCannotCreateRefresh, errors.Unauthorized, err)
	}

	return &token, nil
}

// Standalone generates a standalone jwt token to reset password or other OTA (one time access) actions
func Standalone(user *model.User) (string, error) {
	var expirationMinutes int64 = 15
	if os.Getenv("TOKEN_EXPIRATION_MINUTES") != "" {
		expirationMinutes, _ = strconv.ParseInt(os.Getenv("TOKEN_EXPIRATION_MINUTES"), 10, 64)
	}
	accessTokenClaims := jwt.MapClaims{
		"authorized": true,
		"uid":        user.ID,
		"role":       "user",
		"exp":        time.Now().Add(time.Minute * time.Duration(expirationMinutes)).Unix(),
	}

	if user.IsAdmin() {
		accessTokenClaims["role"] = "admin"
	}
	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessTokenClaims)

	return accessToken.SignedString([]byte(os.Getenv("API_ACCESS_JWT_KEY")))
}

// Extract a jwt token from request headers
func Extract(r *http.Request) string {
	bearerToken := r.Header.Get("Authorization")
	token := strings.Split(bearerToken, " ")

	if len(token) == 2 {
		return token[1]
	}

	return ""
}

func (td *Details) Validate() error {
	if _, ok := td.Token.Claims.(jwt.MapClaims); !ok && !td.Token.Valid {
		return ErrTokenExpired
	}

	return nil
}

// GetClaimsFromToken returns the payload data contained on token
func GetClaimsFromToken(userToken string) (*Details, error) {
	var details Details
	token, err := jwt.Parse(userToken, ParseToken)
	if err != nil {
		return nil, errors.New(errors.Token, errors.TokenInvalid, errors.Unauthorized, err)
	}
	details.Token = token

	claims, ok := token.Claims.(jwt.MapClaims)
	if ok && token.Valid {
		uid, ok := claims["uid"].(string)
		if !ok {
			return nil, errors.New(errors.Token, errors.TokenCannotGetClaims, errors.Unauthorized, err)
		}
		details.Uid = uid

		role, ok := claims["role"].(string)
		if !ok {
			return nil, errors.New(errors.Token, errors.TokenCannotGetClaims, errors.Unauthorized, err)
		}
		details.Role = role
	}

	return &details, nil
}

// GetClaimsFromRefreshToken returns the payload data contained on refresh token
func GetClaimsFromRefreshToken(refreshToken string) (*Details, error) {
	var details Details
	token, err := jwt.Parse(refreshToken, ParseRefreshToken)
	if err != nil {
		return nil, errors.New(errors.Token, errors.TokenInvalid, errors.Unauthorized, err)
	}
	details.Token = token

	claims, ok := token.Claims.(jwt.MapClaims)
	if ok && token.Valid {
		uid, ok := claims["uid"].(string)
		if !ok {
			return nil, errors.New(errors.Token, errors.TokenCannotGetClaims, errors.Unauthorized, err)
		}
		details.Uid = uid

		role, ok := claims["role"].(string)
		if !ok {
			return nil, errors.New(errors.Token, errors.TokenCannotGetClaims, errors.Unauthorized, err)
		}
		details.Role = role
	}

	return &details, nil
}

// GetClaimsFromRequest returns the payload data contained on request token
func GetClaimsFromRequest(r *http.Request) (*Details, error) {
	userToken := Extract(r)
	return GetClaimsFromToken(userToken)
}

func ParseToken(token *jwt.Token) (interface{}, error) {
	if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
		return nil, errors.New(errors.Token, errors.TokenInvalidSigninMethod, errors.Unauthorized, nil)
	}

	return []byte(os.Getenv("API_ACCESS_JWT_KEY")), nil
}

func ParseRefreshToken(token *jwt.Token) (interface{}, error) {
	if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
		return nil, errors.New(errors.Token, errors.TokenInvalidSigninMethod, errors.Unauthorized, nil)
	}

	return []byte(os.Getenv("API_REFRESH_JWT_KEY")), nil
}
