package dashboard

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DashboardRepositoryTestSuite struct {
	suite.Suite
	client     *mongo.Client
	db         *mongo.Database
	repository Repository
	testUserID string
}

func (suite *DashboardRepositoryTestSuite) SetupSuite() {
	// Connect to MongoDB
	mongoURI := os.Getenv("MONGODB_URI")
	if mongoURI == "" {
		mongoURI = "mongodb://localhost:27017"
	}

	client, err := mongo.Connect(context.Background(), options.Client().ApplyURI(mongoURI))
	suite.Require().NoError(err)

	// Use a test database
	suite.client = client
	suite.db = client.Database("dinbora_dashboard_test")
	suite.repository = New(suite.db)
	suite.testUserID = "test-user-123"
}

func (suite *DashboardRepositoryTestSuite) TearDownSuite() {
	// Clean up test database
	err := suite.db.Drop(context.Background())
	suite.Require().NoError(err)

	err = suite.client.Disconnect(context.Background())
	suite.Require().NoError(err)
}

func (suite *DashboardRepositoryTestSuite) SetupTest() {
	// Clean the unified collection before each test
	ctx := context.Background()

	// Drop the unified financial map collection
	coll := suite.db.Collection("dashboard.financialmap")
	err := coll.Drop(ctx)
	if err != nil {
		suite.T().Logf("Warning: failed to drop collection dashboard.financialmap: %v", err)
	}
}

func TestDashboardRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(DashboardRepositoryTestSuite))
}

func (suite *DashboardRepositoryTestSuite) TestFinancialMap_UnifiedCollection() {
	ctx := context.Background()

	// Create a financial map with income sources
	financialMap := &dashboard.FinancialMap{
		UserID:        suite.testUserID,
		MonthlyIncome: 5000,
		IncomeSources: []dashboard.IncomeSource{
			{
				UserID:        suite.testUserID,
				Name:          "Test Salary",
				MonthlyAmount: 5000,
				MoneySource:   financialsheet.MoneySourceOpt1,
			},
		},
		Investments:     []dashboard.Investment{},
		Assets:          []dashboard.Asset{},
		NetWorthHistory: []dashboard.NetWorthSnapshot{},
	}

	// Prepare income sources
	for i := range financialMap.IncomeSources {
		err := financialMap.IncomeSources[i].PrepareCreate()
		suite.Require().NoError(err)
	}

	// Prepare financial map
	err := financialMap.PrepareCreate()
	suite.Require().NoError(err)

	// Save financial map
	err = suite.repository.SaveFinancialMap(ctx, financialMap)
	suite.Require().NoError(err)
	suite.NotEmpty(financialMap.ID)

	// Find financial map
	found, err := suite.repository.FindFinancialMap(ctx, suite.testUserID)
	suite.Require().NoError(err)
	suite.Equal(financialMap.UserID, found.UserID)
	suite.Equal(financialMap.MonthlyIncome, found.MonthlyIncome)
	suite.Len(found.IncomeSources, 1)
	suite.Equal("Test Salary", found.IncomeSources[0].Name)

	// Test individual entity queries (should work with unified collection)
	incomeSources, err := suite.repository.FindIncomeSources(ctx, suite.testUserID)
	suite.Require().NoError(err)
	suite.Len(incomeSources, 1)
	suite.Equal("Test Salary", incomeSources[0].Name)

	// Test finding individual income source by ID
	incomeSourceID := found.IncomeSources[0].ObjectID
	foundIncomeSource, err := suite.repository.FindIncomeSource(ctx, incomeSourceID)
	suite.Require().NoError(err)
	suite.Equal("Test Salary", foundIncomeSource.Name)
}

func (suite *DashboardRepositoryTestSuite) TestDeprecatedMethods_ReturnNotImplemented() {
	ctx := context.Background()

	// Test deprecated income source methods
	incomeSource := &dashboard.IncomeSource{
		UserID:        suite.testUserID,
		Name:          "Test",
		MonthlyAmount: 1000,
		MoneySource:   financialsheet.MoneySourceOpt1,
	}

	err := suite.repository.CreateIncomeSource(ctx, incomeSource)
	suite.Error(err)
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok)
	suite.Equal(errors.NotImplemented, domainErr.Kind())

	err = suite.repository.UpdateIncomeSource(ctx, incomeSource)
	suite.Error(err)
	domainErr, ok = err.(*errors.DomainError)
	suite.True(ok)
	suite.Equal(errors.NotImplemented, domainErr.Kind())

	err = suite.repository.DeleteIncomeSource(ctx, incomeSource.ObjectID)
	suite.Error(err)
	domainErr, ok = err.(*errors.DomainError)
	suite.True(ok)
	suite.Equal(errors.NotImplemented, domainErr.Kind())

	// Test deprecated emergency fund methods
	emergencyFund := &dashboard.EmergencyFund{
		UserID:       suite.testUserID,
		CurrentValue: 1000,
		GoalValue:    5000,
	}

	err = suite.repository.CreateEmergencyFund(ctx, emergencyFund)
	suite.Error(err)
	domainErr, ok = err.(*errors.DomainError)
	suite.True(ok)
	suite.Equal(errors.NotImplemented, domainErr.Kind())

	err = suite.repository.UpdateEmergencyFund(ctx, emergencyFund)
	suite.Error(err)
	domainErr, ok = err.(*errors.DomainError)
	suite.True(ok)
	suite.Equal(errors.NotImplemented, domainErr.Kind())
}

func (suite *DashboardRepositoryTestSuite) TestEmergencyFund_UnifiedCollection() {
	ctx := context.Background()

	// Create a financial map with emergency fund
	financialMap := &dashboard.FinancialMap{
		UserID:        suite.testUserID,
		MonthlyIncome: 0,
		EmergencyFund: &dashboard.EmergencyFund{
			UserID:       suite.testUserID,
			CurrentValue: 10000,
			GoalValue:    50000,
		},
		IncomeSources:   []dashboard.IncomeSource{},
		Investments:     []dashboard.Investment{},
		Assets:          []dashboard.Asset{},
		NetWorthHistory: []dashboard.NetWorthSnapshot{},
	}

	// Prepare emergency fund
	err := financialMap.EmergencyFund.PrepareCreate()
	suite.Require().NoError(err)

	// Prepare financial map
	err = financialMap.PrepareCreate()
	suite.Require().NoError(err)

	// Save financial map
	err = suite.repository.SaveFinancialMap(ctx, financialMap)
	suite.Require().NoError(err)

	// Test finding emergency fund through unified collection
	found, err := suite.repository.FindEmergencyFund(ctx, suite.testUserID)
	suite.Require().NoError(err)
	suite.Equal(monetary.Amount(10000), found.CurrentValue)
	suite.Equal(monetary.Amount(50000), found.GoalValue)

	// Test case where no emergency fund exists
	_, err = suite.repository.FindEmergencyFund(ctx, "nonexistent-user")
	suite.Error(err)
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok)
	suite.Equal(errors.NotFound, domainErr.Kind())
}

func (suite *DashboardRepositoryTestSuite) TestInvestments_UnifiedCollection() {
	ctx := context.Background()

	// Create a financial map with investments
	financialMap := &dashboard.FinancialMap{
		UserID:        suite.testUserID,
		MonthlyIncome: 0,
		Investments: []dashboard.Investment{
			{
				UserID:       suite.testUserID,
				Name:         "Tesouro Selic",
				CurrentValue: 25000,
			},
			{
				UserID:       suite.testUserID,
				Name:         "CDB",
				CurrentValue: 15000,
			},
		},
		IncomeSources:   []dashboard.IncomeSource{},
		Assets:          []dashboard.Asset{},
		NetWorthHistory: []dashboard.NetWorthSnapshot{},
	}

	// Prepare investments
	for i := range financialMap.Investments {
		err := financialMap.Investments[i].PrepareCreate()
		suite.Require().NoError(err)
	}

	// Prepare financial map
	err := financialMap.PrepareCreate()
	suite.Require().NoError(err)

	// Save financial map
	err = suite.repository.SaveFinancialMap(ctx, financialMap)
	suite.Require().NoError(err)

	// Test finding investments through unified collection
	investments, err := suite.repository.FindInvestments(ctx, suite.testUserID)
	suite.Require().NoError(err)
	suite.Len(investments, 2)
	suite.Equal("Tesouro Selic", investments[0].Name)
	suite.Equal("CDB", investments[1].Name)

	// Test finding individual investment by ID
	investmentID := investments[0].ObjectID
	foundInvestment, err := suite.repository.FindInvestment(ctx, investmentID)
	suite.Require().NoError(err)
	suite.Equal("Tesouro Selic", foundInvestment.Name)
}

func (suite *DashboardRepositoryTestSuite) TestAssets_UnifiedCollection() {
	ctx := context.Background()

	// Create a financial map with assets
	financialMap := &dashboard.FinancialMap{
		UserID:        suite.testUserID,
		MonthlyIncome: 0,
		Assets: []dashboard.Asset{
			{
				UserID:      suite.testUserID,
				Description: "Honda Civic",
				Value:       80000,
			},
			{
				UserID:      suite.testUserID,
				Description: "Apartment",
				Value:       300000,
			},
		},
		IncomeSources:   []dashboard.IncomeSource{},
		Investments:     []dashboard.Investment{},
		NetWorthHistory: []dashboard.NetWorthSnapshot{},
	}

	// Prepare assets
	for i := range financialMap.Assets {
		err := financialMap.Assets[i].PrepareCreate()
		suite.Require().NoError(err)
	}

	// Prepare financial map
	err := financialMap.PrepareCreate()
	suite.Require().NoError(err)

	// Save financial map
	err = suite.repository.SaveFinancialMap(ctx, financialMap)
	suite.Require().NoError(err)

	// Test finding assets through unified collection
	assets, err := suite.repository.FindAssets(ctx, suite.testUserID)
	suite.Require().NoError(err)
	suite.Len(assets, 2)
	suite.Equal("Honda Civic", assets[0].Description)
	suite.Equal("Apartment", assets[1].Description)

	// Test finding individual asset by ID
	assetID := assets[0].ObjectID
	foundAsset, err := suite.repository.FindAsset(ctx, assetID)
	suite.Require().NoError(err)
	suite.Equal("Honda Civic", foundAsset.Description)
}

func (suite *DashboardRepositoryTestSuite) TestNetWorthHistory_UnifiedCollection() {
	ctx := context.Background()

	// Create a financial map with net worth history
	financialMap := &dashboard.FinancialMap{
		UserID:        suite.testUserID,
		MonthlyIncome: 0,
		NetWorthHistory: []dashboard.NetWorthSnapshot{
			{
				UserID:             suite.testUserID,
				Date:               time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				EmergencyFundValue: 5000,
				InvestmentsValue:   10000,
				AssetsValue:        50000,
			},
			{
				UserID:             suite.testUserID,
				Date:               time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC),
				EmergencyFundValue: 7000,
				InvestmentsValue:   12000,
				AssetsValue:        50000,
			},
			{
				UserID:             suite.testUserID,
				Date:               time.Date(2024, 3, 1, 0, 0, 0, 0, time.UTC),
				EmergencyFundValue: 10000,
				InvestmentsValue:   15000,
				AssetsValue:        50000,
			},
		},
		IncomeSources: []dashboard.IncomeSource{},
		Investments:   []dashboard.Investment{},
		Assets:        []dashboard.Asset{},
	}

	// Prepare net worth snapshots
	for i := range financialMap.NetWorthHistory {
		err := financialMap.NetWorthHistory[i].PrepareCreate()
		suite.Require().NoError(err)
	}

	// Prepare financial map
	err := financialMap.PrepareCreate()
	suite.Require().NoError(err)

	// Save financial map
	err = suite.repository.SaveFinancialMap(ctx, financialMap)
	suite.Require().NoError(err)

	// Test finding net worth history with limit
	history, err := suite.repository.FindNetWorthHistory(ctx, suite.testUserID, 2)
	suite.Require().NoError(err)
	suite.Len(history, 2)

	// Should be ordered by date descending (most recent first)
	suite.True(history[0].Date.After(history[1].Date))

	// Test finding all history
	allHistory, err := suite.repository.FindNetWorthHistory(ctx, suite.testUserID, 10)
	suite.Require().NoError(err)
	suite.Len(allHistory, 3)

	// Test case where no history exists
	emptyHistory, err := suite.repository.FindNetWorthHistory(ctx, "nonexistent-user", 10)
	suite.Require().NoError(err)
	suite.Len(emptyHistory, 0)
}
