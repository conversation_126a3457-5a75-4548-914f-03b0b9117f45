package dashboard

import (
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestIncomeSource_PrepareCreate(t *testing.T) {
	tests := []struct {
		name        string
		incomeSource *IncomeSource
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid income source",
			incomeSource: &IncomeSource{
				UserID:        "user123",
				Name:          "Salary",
				MonthlyAmount: 5000,
				MoneySource:   financialsheet.MoneySourceOpt1,
			},
			expectError: false,
		},
		{
			name: "empty name",
			incomeSource: &IncomeSource{
				UserID:        "user123",
				Name:          "",
				MonthlyAmount: 5000,
				MoneySource:   financialsheet.MoneySourceOpt1,
			},
			expectError: true,
			errorMsg:    "income source name is required",
		},
		{
			name: "whitespace only name",
			incomeSource: &IncomeSource{
				UserID:        "user123",
				Name:          "   ",
				MonthlyAmount: 5000,
				MoneySource:   financialsheet.MoneySourceOpt1,
			},
			expectError: true,
			errorMsg:    "income source name is required",
		},
		{
			name: "zero monthly amount",
			incomeSource: &IncomeSource{
				UserID:        "user123",
				Name:          "Salary",
				MonthlyAmount: 0,
				MoneySource:   financialsheet.MoneySourceOpt1,
			},
			expectError: true,
			errorMsg:    "monthly amount must be positive",
		},
		{
			name: "negative monthly amount",
			incomeSource: &IncomeSource{
				UserID:        "user123",
				Name:          "Salary",
				MonthlyAmount: -1000,
				MoneySource:   financialsheet.MoneySourceOpt1,
			},
			expectError: true,
			errorMsg:    "monthly amount must be positive",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.incomeSource.PrepareCreate()

			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				
				// Check that it's a domain error with correct kind
				domainErr, ok := err.(*errors.DomainError)
				require.True(t, ok)
				assert.Equal(t, errors.Validation, domainErr.Kind())
			} else {
				require.NoError(t, err)
				
				// Check that timestamps are set
				assert.False(t, tt.incomeSource.CreatedAt.IsZero())
				assert.False(t, tt.incomeSource.UpdatedAt.IsZero())
				assert.False(t, tt.incomeSource.LastUpdated.IsZero())
				
				// Check that name is trimmed
				assert.Equal(t, "Salary", tt.incomeSource.Name)
			}
		})
	}
}

func TestIncomeSource_PrepareUpdate(t *testing.T) {
	incomeSource := &IncomeSource{
		UserID:        "user123",
		Name:          "  Updated Salary  ",
		MonthlyAmount: 6000,
		MoneySource:   financialsheet.MoneySourceOpt2,
		CreatedAt:     time.Now().Add(-time.Hour),
	}

	err := incomeSource.PrepareUpdate()
	require.NoError(t, err)

	// Check that name is trimmed
	assert.Equal(t, "Updated Salary", incomeSource.Name)
	
	// Check that UpdatedAt and LastUpdated are set
	assert.False(t, incomeSource.UpdatedAt.IsZero())
	assert.False(t, incomeSource.LastUpdated.IsZero())
	
	// Check that CreatedAt is not changed
	assert.True(t, incomeSource.CreatedAt.Before(incomeSource.UpdatedAt))
}

func TestEmergencyFund_PrepareCreate(t *testing.T) {
	tests := []struct {
		name          string
		emergencyFund *EmergencyFund
		expectError   bool
		errorMsg      string
	}{
		{
			name: "valid emergency fund",
			emergencyFund: &EmergencyFund{
				UserID:       "user123",
				CurrentValue: 10000,
				GoalValue:    50000,
			},
			expectError: false,
		},
		{
			name: "zero current value",
			emergencyFund: &EmergencyFund{
				UserID:       "user123",
				CurrentValue: 0,
				GoalValue:    50000,
			},
			expectError: false,
		},
		{
			name: "negative current value",
			emergencyFund: &EmergencyFund{
				UserID:       "user123",
				CurrentValue: -1000,
				GoalValue:    50000,
			},
			expectError: true,
			errorMsg:    "current value cannot be negative",
		},
		{
			name: "zero goal value",
			emergencyFund: &EmergencyFund{
				UserID:       "user123",
				CurrentValue: 10000,
				GoalValue:    0,
			},
			expectError: true,
			errorMsg:    "goal value must be positive",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.emergencyFund.PrepareCreate()

			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
				assert.False(t, tt.emergencyFund.CreatedAt.IsZero())
				assert.False(t, tt.emergencyFund.UpdatedAt.IsZero())
			}
		})
	}
}

func TestInvestment_PrepareCreate(t *testing.T) {
	investment := &Investment{
		UserID:       "user123",
		Name:         "  Tesouro Selic  ",
		CurrentValue: 25000,
	}

	err := investment.PrepareCreate()
	require.NoError(t, err)

	// Check that name is trimmed
	assert.Equal(t, "Tesouro Selic", investment.Name)
	
	// Check that timestamps are set
	assert.False(t, investment.CreatedAt.IsZero())
	assert.False(t, investment.UpdatedAt.IsZero())
}

func TestAsset_PrepareCreate(t *testing.T) {
	asset := &Asset{
		UserID:      "user123",
		Description: "  Carro Honda Civic  ",
		Value:       80000,
	}

	err := asset.PrepareCreate()
	require.NoError(t, err)

	// Check that description is trimmed
	assert.Equal(t, "Carro Honda Civic", asset.Description)
	
	// Check that timestamps are set
	assert.False(t, asset.CreatedAt.IsZero())
	assert.False(t, asset.UpdatedAt.IsZero())
}

func TestNetWorthSnapshot_PrepareCreate(t *testing.T) {
	snapshot := &NetWorthSnapshot{
		UserID:             "user123",
		EmergencyFundValue: 10000,
		InvestmentsValue:   25000,
		AssetsValue:        80000,
	}

	err := snapshot.PrepareCreate()
	require.NoError(t, err)

	// Check that date is set if not provided
	assert.False(t, snapshot.Date.IsZero())
	
	// Check that total value is calculated
	expectedTotal := monetary.Amount(10000 + 25000 + 80000)
	assert.Equal(t, expectedTotal, snapshot.TotalValue)
	
	// Check that CreatedAt is set
	assert.False(t, snapshot.CreatedAt.IsZero())
}

func TestNetWorthSnapshot_PrepareCreate_WithDate(t *testing.T) {
	customDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	snapshot := &NetWorthSnapshot{
		UserID:             "user123",
		Date:               customDate,
		EmergencyFundValue: 5000,
		InvestmentsValue:   15000,
		AssetsValue:        40000,
	}

	err := snapshot.PrepareCreate()
	require.NoError(t, err)

	// Check that custom date is preserved
	assert.Equal(t, customDate, snapshot.Date)
	
	// Check that total value is calculated
	expectedTotal := monetary.Amount(5000 + 15000 + 40000)
	assert.Equal(t, expectedTotal, snapshot.TotalValue)
}
